# <PERSON> Bahcheli

import argparse, time, os
import sys
import csv
import re
import glob

help_message = '''
Generate Nextflow-compatible samplesheet.csv from downloaded FASTQ files
'''

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Generate Nextflow samplesheet from downloaded FASTQ files"
    )
    parser.add_argument(
        "--work_dir",
        required=True,
        help="Working directory containing FASTQ files"
    )
    parser.add_argument(
        "--output",
        required=True,
        help="Output samplesheet CSV file"
    )
    parser.add_argument(
        "--strandedness",
        default="auto",
        help="RNA-seq strandedness (auto, forward, reverse, unstranded)"
    )

    return parser.parse_args()

def pair_fastq_files(fastq_files):
    """
    Pair R1 and R2 FASTQ files
    
    Args:
        fastq_files (list): List of FASTQ file paths
    
    Returns:
        list: List of tuples (sample_name, r1_file, r2_file)
    """
    
    pairs = []
    r1_files = []
    r2_files = []
    
    # Separate R1 and R2 files
    for file_path in fastq_files:
        filename = os.path.basename(file_path)
        if '_R1' in filename or '_1.fastq' in filename or '_1.fq' in filename:
            r1_files.append(file_path)
        elif '_R2' in filename or '_2.fastq' in filename or '_2.fq' in filename:
            r2_files.append(file_path)
    
    print(f"Found {len(r1_files)} R1 files and {len(r2_files)} R2 files")
    
    # Match R1 and R2 files
    for r1_file in r1_files:
        r1_basename = os.path.basename(r1_file)
        
        # Try to find corresponding R2 file
        r2_file = None
        for r2_candidate in r2_files:
            r2_basename = os.path.basename(r2_candidate)
            
            # Create pattern to match R1 with R2
            r1_pattern = r1_basename.replace('_R1', '_R2').replace('_1.fastq', '_2.fastq').replace('_1.fq', '_2.fq')
            
            if r2_basename == r1_pattern:
                r2_file = r2_candidate
                break
        
        if r2_file:
            # Extract sample name from filename
            sample_name = extract_sample_name(r1_basename)
            pairs.append((sample_name, r1_file, r2_file))
            r2_files.remove(r2_file)  # Remove to avoid duplicate pairing
        else:
            print(f"WARNING: No R2 file found for {r1_file}")
    
    return pairs

def extract_sample_name(filename):
    """
    Extract sample name from FASTQ filename
    
    Args:
        filename (str): FASTQ filename
    
    Returns:
        str: Extracted sample name
    """
    
    # Remove common FASTQ file extensions and suffixes
    name = filename
    
    # Remove extensions
    for ext in ['.fastq.gz', '.fq.gz', '.fastq', '.fq']:
        if name.endswith(ext):
            name = name[:-len(ext)]
            break
    
    # Remove R1/R2 indicators
    patterns = ['_R1', '_R2', '_1', '_2']
    for pattern in patterns:
        if pattern in name:
            name = name.split(pattern)[0]
            break
    
    # Remove lane information if present (e.g., _L001)
    name = re.sub(r'_L\d+', '', name)
    
    return name

def scan_downloaded_fastq(work_dir):
    """
    Scan downloaded FASTQ files and organize by sample
    
    Args:
        work_dir (str): Working directory containing fastq subdirectory
    
    Returns:
        dict: Dictionary mapping sample_id to list of FASTQ files
    """
    
    fastq_dir = os.path.join(work_dir, "fastq")
    
    if not os.path.exists(fastq_dir):
        print(f"ERROR: FASTQ directory not found: {fastq_dir}")
        return {}
    
    sample_fastq = {}
    
    # Scan each sample directory
    for sample_dir in os.listdir(fastq_dir):
        sample_path = os.path.join(fastq_dir, sample_dir)
        
        if os.path.isdir(sample_path):
            # Find all FASTQ files in sample directory
            fastq_files = []
            for ext in ['*.fastq.gz', '*.fq.gz', '*.fastq', '*.fq']:
                fastq_files.extend(glob.glob(os.path.join(sample_path, ext)))
            
            if fastq_files:
                sample_fastq[sample_dir] = sorted(fastq_files)
                print(f"Sample {sample_dir}: {len(fastq_files)} FASTQ files")
    
    return sample_fastq

def generate_samplesheet(work_dir, output_file, strandedness='auto'):
    """
    Generate Nextflow samplesheet from downloaded FASTQ files
    
    Args:
        work_dir (str): Working directory
        output_file (str): Output samplesheet file path
        strandedness (str): RNA-seq strandedness (auto, forward, reverse, unstranded)
    
    Returns:
        bool: True if successful, False otherwise
    """
    
    # Scan downloaded FASTQ files
    sample_fastq = scan_downloaded_fastq(work_dir)
    
    if not sample_fastq:
        print("ERROR: No FASTQ files found")
        return False
    
    # Generate samplesheet entries
    samplesheet_entries = []
    
    for sample_id, fastq_files in sample_fastq.items():
        # Pair R1 and R2 files
        pairs = pair_fastq_files(fastq_files)
        
        if not pairs:
            print(f"WARNING: No valid R1/R2 pairs found for sample {sample_id}")
            continue
        
        # Add each pair to samplesheet
        for i, (sample_name, r1_file, r2_file) in enumerate(pairs):
            # If multiple pairs for same sample, add suffix
            if len(pairs) > 1:
                entry_name = f"{sample_id}_L{i+1:03d}"
            else:
                entry_name = sample_id
            
            samplesheet_entries.append({
                'sample': entry_name,
                'fastq_1': r1_file,
                'fastq_2': r2_file,
                'strandedness': strandedness
            })
    
    if not samplesheet_entries:
        print("ERROR: No valid samplesheet entries generated")
        return False
    
    # Write samplesheet
    try:
        with open(output_file, 'w', newline='') as csvfile:
            fieldnames = ['sample', 'fastq_1', 'fastq_2', 'strandedness']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for entry in samplesheet_entries:
                writer.writerow(entry)
        
        print(f"Samplesheet generated: {output_file}")
        print(f"Total entries: {len(samplesheet_entries)}")
        
        # Display first few entries for verification
        print("\nFirst few entries:")
        for i, entry in enumerate(samplesheet_entries[:5]):
            print(f"  {entry['sample']}: {os.path.basename(entry['fastq_1'])} + {os.path.basename(entry['fastq_2'])}")
        
        if len(samplesheet_entries) > 5:
            print(f"  ... and {len(samplesheet_entries) - 5} more entries")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to write samplesheet: {e}")
        return False

def main():
    print('generate_samplesheet.py')
    t1 = time.time()

    # load arguments from path
    args = parse_arguments()

    # Generate samplesheet
    success = generate_samplesheet(args.work_dir, args.output, args.strandedness)

    if not success:
        print("ERROR: Failed to generate samplesheet")
        sys.exit(1)

    print(round(time.time() - t1, 2))
    print('generate_samplesheet.py COMPLETE')

if __name__ == "__main__":
    main()
