# Alec Ba<PERSON>cheli

import argparse, time, os
import sys
import csv
import json
from datetime import datetime

help_message = '''
Update master sample sheet with processed samples tracking
'''

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Update master sample sheet with processed samples"
    )
    parser.add_argument(
        "--master_file",
        required=True,
        help="Path to master sample sheet CSV file"
    )
    parser.add_argument(
        "--selected_samples",
        required=True,
        help="Path to selected samples JSON file"
    )
    parser.add_argument(
        "--samplesheet",
        required=True,
        help="Path to current samplesheet CSV file"
    )

    return parser.parse_args()

def load_existing_master_sheet(master_file):
    """
    Load existing master sample sheet
    
    Args:
        master_file (str): Path to master sample sheet
    
    Returns:
        list: List of existing records
    """
    
    if not os.path.exists(master_file):
        print(f"Creating new master sample sheet: {master_file}")
        return []
    
    try:
        with open(master_file, 'r', newline='') as f:
            reader = csv.DictReader(f)
            records = list(reader)
        print(f"Loaded {len(records)} existing records from master sheet")
        return records
    except Exception as e:
        print(f"ERROR: Failed to load master sheet: {e}")
        return []

def load_selected_samples(selected_samples_file):
    """
    Load selected samples information
    
    Args:
        selected_samples_file (str): Path to selected samples JSON
    
    Returns:
        dict: Selected samples data
    """
    
    try:
        with open(selected_samples_file, 'r') as f:
            selected_samples = json.load(f)
        print(f"Loaded information for {len(selected_samples)} selected samples")
        return selected_samples
    except Exception as e:
        print(f"ERROR: Failed to load selected samples: {e}")
        return {}

def load_current_samplesheet(samplesheet_file):
    """
    Load current Nextflow samplesheet
    
    Args:
        samplesheet_file (str): Path to current samplesheet
    
    Returns:
        list: List of samplesheet entries
    """
    
    try:
        with open(samplesheet_file, 'r', newline='') as f:
            reader = csv.DictReader(f)
            entries = list(reader)
        print(f"Loaded {len(entries)} entries from current samplesheet")
        return entries
    except Exception as e:
        print(f"ERROR: Failed to load current samplesheet: {e}")
        return []

def get_file_info(file_path):
    """
    Get file information (size, modification time)
    
    Args:
        file_path (str): Path to file
    
    Returns:
        dict: File information
    """
    
    if not os.path.exists(file_path):
        return {'size_mb': 0, 'exists': False}
    
    try:
        stat = os.stat(file_path)
        return {
            'size_mb': round(stat.st_size / (1024 * 1024), 2),
            'exists': True,
            'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
        }
    except Exception:
        return {'size_mb': 0, 'exists': False}

def create_master_records(selected_samples, samplesheet_entries, batch_id):
    """
    Create master records from current processing batch
    
    Args:
        selected_samples (dict): Selected samples data
        samplesheet_entries (list): Current samplesheet entries
        batch_id (str): Unique batch identifier
    
    Returns:
        list: List of master records
    """
    
    records = []
    processing_date = datetime.now().isoformat()
    
    # Create a mapping from samplesheet entries to sample info
    sample_to_entries = {}
    for entry in samplesheet_entries:
        sample_name = entry['sample']
        # Extract original sample ID (remove lane suffixes)
        original_sample_id = sample_name.split('_L')[0]
        
        if original_sample_id not in sample_to_entries:
            sample_to_entries[original_sample_id] = []
        sample_to_entries[original_sample_id].append(entry)
    
    # Create records for each sample
    for sample_id, sample_data in selected_samples.items():
        sample_info = sample_data.get('sample_info', {})
        
        # Get samplesheet entries for this sample
        entries = sample_to_entries.get(sample_id, [])
        
        # Calculate total file sizes
        total_r1_size = 0
        total_r2_size = 0
        num_lanes = len(entries)
        
        fastq_files_info = []
        for entry in entries:
            r1_info = get_file_info(entry['fastq_1'])
            r2_info = get_file_info(entry['fastq_2'])
            
            total_r1_size += r1_info['size_mb']
            total_r2_size += r2_info['size_mb']
            
            fastq_files_info.append({
                'lane': entry['sample'],
                'r1_file': entry['fastq_1'],
                'r2_file': entry['fastq_2'],
                'r1_size_mb': r1_info['size_mb'],
                'r2_size_mb': r2_info['size_mb'],
                'r1_exists': r1_info['exists'],
                'r2_exists': r2_info['exists']
            })
        
        # Create master record
        record = {
            'batch_id': batch_id,
            'processing_date': processing_date,
            'sample_id': sample_id,
            'patient_id': sample_info.get('patient_id', 'unknown'),
            'sample_type': sample_info.get('sample_type', 'unknown'),
            'primary_tumor_location': sample_info.get('primary_tumor_location', 'unknown'),
            'num_fastq_files': len(sample_data.get('fastq_files', [])),
            'num_lanes': num_lanes,
            'total_r1_size_mb': round(total_r1_size, 2),
            'total_r2_size_mb': round(total_r2_size, 2),
            'total_size_mb': round(total_r1_size + total_r2_size, 2),
            'download_status': 'completed' if entries else 'failed',
            'nextflow_status': 'prepared',
            'notes': f"Downloaded {len(sample_data.get('fastq_files', []))} FASTQ files"
        }
        
        records.append(record)
    
    return records

def update_master_samplesheet(master_file, selected_samples, samplesheet_entries):
    """
    Update the master sample sheet with new records
    
    Args:
        master_file (str): Path to master sample sheet
        selected_samples (dict): Selected samples data
        samplesheet_entries (list): Current samplesheet entries
    
    Returns:
        bool: True if successful
    """
    
    # Load existing records
    existing_records = load_existing_master_sheet(master_file)
    
    # Generate batch ID
    batch_id = datetime.now().strftime("batch_%Y%m%d_%H%M%S")
    
    # Create new records
    new_records = create_master_records(selected_samples, samplesheet_entries, batch_id)
    
    # Combine with existing records
    all_records = existing_records + new_records
    
    # Define fieldnames for CSV
    fieldnames = [
        'batch_id', 'processing_date', 'sample_id', 'patient_id', 
        'sample_type', 'primary_tumor_location', 'num_fastq_files', 
        'num_lanes', 'total_r1_size_mb', 'total_r2_size_mb', 'total_size_mb',
        'download_status', 'nextflow_status', 'notes'
    ]
    
    try:
        # Write updated master sheet
        with open(master_file, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for record in all_records:
                # Ensure all fields are present
                complete_record = {field: record.get(field, '') for field in fieldnames}
                writer.writerow(complete_record)
        
        print(f"✓ Updated master sample sheet: {master_file}")
        print(f"  - Added {len(new_records)} new records")
        print(f"  - Total records: {len(all_records)}")
        print(f"  - Current batch: {batch_id}")
        
        # Print summary of new records
        if new_records:
            print("\nNew records added:")
            total_size = sum(r['total_size_mb'] for r in new_records)
            total_files = sum(r['num_fastq_files'] for r in new_records)
            
            print(f"  - Samples: {len(new_records)}")
            print(f"  - Total FASTQ files: {total_files}")
            print(f"  - Total data size: {total_size:.2f} MB ({total_size/1024:.2f} GB)")
            
            # Show first few samples
            for i, record in enumerate(new_records[:5]):
                print(f"  - {record['sample_id']}: {record['num_fastq_files']} files, "
                      f"{record['total_size_mb']:.1f} MB")
            
            if len(new_records) > 5:
                print(f"  ... and {len(new_records) - 5} more samples")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to update master sheet: {e}")
        return False

def generate_summary_report(master_file):
    """
    Generate a summary report from the master sample sheet
    
    Args:
        master_file (str): Path to master sample sheet
    """
    
    if not os.path.exists(master_file):
        return
    
    try:
        with open(master_file, 'r', newline='') as f:
            reader = csv.DictReader(f)
            records = list(reader)
        
        if not records:
            return
        
        # Generate summary
        total_samples = len(records)
        total_size_gb = sum(float(r.get('total_size_mb', 0)) for r in records) / 1024
        total_files = sum(int(r.get('num_fastq_files', 0)) for r in records)
        
        # Count by status
        download_status = {}
        nextflow_status = {}
        
        for record in records:
            dl_status = record.get('download_status', 'unknown')
            nf_status = record.get('nextflow_status', 'unknown')
            
            download_status[dl_status] = download_status.get(dl_status, 0) + 1
            nextflow_status[nf_status] = nextflow_status.get(nf_status, 0) + 1
        
        # Count unique batches
        batches = set(r.get('batch_id', '') for r in records)
        
        print(f"\n{'='*50}")
        print("MASTER SAMPLE SHEET SUMMARY")
        print(f"{'='*50}")
        print(f"Total samples processed: {total_samples}")
        print(f"Total FASTQ files: {total_files}")
        print(f"Total data size: {total_size_gb:.2f} GB")
        print(f"Processing batches: {len(batches)}")
        
        print(f"\nDownload status:")
        for status, count in download_status.items():
            print(f"  - {status}: {count}")
        
        print(f"\nNextflow status:")
        for status, count in nextflow_status.items():
            print(f"  - {status}: {count}")
        
    except Exception as e:
        print(f"ERROR: Failed to generate summary: {e}")

def main():
    print('update_master_samplesheet.py')
    t1 = time.time()

    # load arguments from path
    args = parse_arguments()

    # Load data
    selected_samples = load_selected_samples(args.selected_samples)
    samplesheet_entries = load_current_samplesheet(args.samplesheet)

    if not selected_samples or not samplesheet_entries:
        print("ERROR: Failed to load required data")
        sys.exit(1)

    # Update master sample sheet
    success = update_master_samplesheet(args.master_file, selected_samples, samplesheet_entries)

    if success:
        generate_summary_report(args.master_file)
        print("Master sample sheet update completed successfully")
    else:
        print("ERROR: Failed to update master sample sheet")
        sys.exit(1)

    print(round(time.time() - t1, 2))
    print('update_master_samplesheet.py COMPLETE')

if __name__ == "__main__":
    main()
