# Alec Bahcheli

import argparse, time, os
import json
import sys
import subprocess
import concurrent.futures

help_message = '''
Download FASTQ files from Google Cloud Storage using gsutil
'''

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Download HMF RNA-seq FASTQ files from Google Cloud"
    )
    parser.add_argument(
        "--selected_samples",
        required=True,
        help="Path to selected samples JSON file"
    )
    parser.add_argument(
        "--work_dir",
        required=True,
        help="Working directory for downloads"
    )
    parser.add_argument(
        "--project_id",
        required=True,
        help="Google Cloud project ID"
    )
    parser.add_argument(
        "--output",
        required=True,
        help="Output completion flag file"
    )
    parser.add_argument(
        "--threads",
        type=int,
        default=1,
        help="Maximum concurrent downloads"
    )

    return parser.parse_args()

def download_file(url, output_path, project_id, max_retries=3):
    """
    Download a single FASTQ file from Google Cloud Storage
    
    Args:
        url (str): Google Cloud Storage URL
        output_path (str): Local output path
        project_id (str): GCP project ID
        max_retries (int): Maximum number of retry attempts
    
    Returns:
        tuple: (success, url, output_path, error_message)
    """
    
    for attempt in range(max_retries):
        try:
            print(f"Downloading {url} to {output_path} (attempt {attempt + 1}/{max_retries})")
            
            # Create output directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Use gsutil to download the file
            cmd = ["gsutil", "-u", project_id, "cp", url, output_path]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # Verify file was downloaded and has content
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                print(f"Successfully downloaded: {output_path}")
                return True, url, output_path, None
            else:
                raise Exception("Downloaded file is empty or doesn't exist")
                
        except subprocess.CalledProcessError as e:
            error_msg = f"gsutil error: {e.stderr}"
            print(f"Download failed (attempt {attempt + 1}): {error_msg}")
            
            if attempt == max_retries - 1:
                return False, url, output_path, error_msg
            
            # Wait before retry
            time.sleep(2 ** attempt)  # Exponential backoff
            
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            print(f"Download failed (attempt {attempt + 1}): {error_msg}")
            
            if attempt == max_retries - 1:
                return False, url, output_path, error_msg
            
            time.sleep(2 ** attempt)
    
    return False, url, output_path, "Max retries exceeded"

def download_sample_fastq(sample_id, sample_data, work_dir, project_id, threads=4):
    """
    Download all FASTQ files for a single sample
    
    Args:
        sample_id (str): Sample identifier
        sample_data (dict): Sample data containing fastq file information
        work_dir (str): Working directory
        project_id (str): GCP project ID
        threads (int): Maximum number of concurrent downloads
    
    Returns:
        tuple: (success, downloaded_files, failed_files)
    """
    
    fastq_files = sample_data['fastq_files']
    sample_dir = os.path.join(work_dir, "fastq", sample_id)
    
    print(f"Downloading {len(fastq_files)} FASTQ files for sample {sample_id}")
    
    # Prepare download tasks
    download_tasks = []
    for i, fastq_info in enumerate(fastq_files):
        url = fastq_info['url']
        filename = os.path.basename(url)
        output_path = os.path.join(sample_dir, filename)
        download_tasks.append((url, output_path, project_id))
    
    # Download files concurrently
    downloaded_files = []
    failed_files = []
    
    with concurrent.futures.ThreadPoolExecutor(threads=threads) as executor:
        # Submit all download tasks
        future_to_task = {
            executor.submit(download_file, url, output_path, project_id): (url, output_path)
            for url, output_path, project_id in download_tasks
        }
        
        # Collect results
        for future in concurrent.futures.as_completed(future_to_task):
            url, output_path = future_to_task[future]
            try:
                success, _, _, error_msg = future.result()
                if success:
                    downloaded_files.append(output_path)
                else:
                    failed_files.append((url, error_msg))
            except Exception as e:
                failed_files.append((url, str(e)))
    
    success = len(failed_files) == 0
    print(f"Sample {sample_id}: {len(downloaded_files)} downloaded, {len(failed_files)} failed")
    
    return success, downloaded_files, failed_files

def download_all_samples(selected_samples_file, work_dir, project_id, threads=4):
    """
    Download FASTQ files for all selected samples
    
    Args:
        selected_samples_file (str): Path to selected samples JSON file
        work_dir (str): Working directory
        project_id (str): GCP project ID
        threads (int): Maximum number of concurrent downloads per sample
    
    Returns:
        bool: True if all downloads successful, False otherwise
    """
    
    # Load selected samples
    try:
        with open(selected_samples_file, 'r') as f:
            selected_samples = json.load(f)
    except Exception as e:
        print(f"ERROR: Failed to load selected samples: {e}")
        return False
    
    print(f"Starting download of FASTQ files for {len(selected_samples)} samples")
    
    all_success = True
    total_downloaded = 0
    total_failed = 0
    
    # Download each sample
    for sample_id, sample_data in selected_samples.items():
        success, downloaded_files, failed_files = download_sample_fastq(
            sample_id, sample_data, work_dir, project_id, threads
        )
        
        total_downloaded += len(downloaded_files)
        total_failed += len(failed_files)
        
        if not success:
            all_success = False
            print(f"WARNING: Some files failed to download for sample {sample_id}")
            for url, error in failed_files:
                print(f"  Failed: {url} - {error}")
    
    print(f"Download summary: {total_downloaded} files downloaded, {total_failed} files failed")
    
    return all_success

def main():
    print('download_fastq.py')
    t1 = time.time()

    # load arguments from path
    args = parse_arguments()

    # Download all samples
    success = download_all_samples(args.selected_samples, args.work_dir, args.project_id, args.threads)

    if success:
        # Create completion flag
        with open(args.output, 'w') as f:
            f.write("Download completed successfully\n")
        print("All FASTQ files downloaded successfully")
    else:
        print("ERROR: Some FASTQ files failed to download")
        sys.exit(1)

    print(round(time.time() - t1, 2))
    print('download_fastq.py COMPLETE')

if __name__ == "__main__":
    main()
