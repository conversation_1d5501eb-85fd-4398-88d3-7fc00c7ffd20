#!/usr/bin/env python3
"""
Google Cloud Platform authentication script for HMF data access
This script handles authentication using a service account key file
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def authenticate_gcp(key_file_path, project_id):
    """
    Authenticate with Google Cloud Platform using service account key
    
    Args:
        key_file_path (str): Path to the service account key JSON file
        project_id (str): GCP project ID
    
    Returns:
        bool: True if authentication successful, False otherwise
    """
    
    # Check if key file exists
    if not os.path.exists(key_file_path):
        print(f"ERROR: Service account key file not found: {key_file_path}")
        return False
    
    try:
        # Activate service account
        print(f"Authenticating with service account key: {key_file_path}")
        result = subprocess.run([
            "gcloud", "auth", "activate-service-account", 
            "--key-file", key_file_path
        ], capture_output=True, text=True, check=True)
        
        print("Service account authentication successful")
        
        # Set project
        print(f"Setting project to: {project_id}")
        result = subprocess.run([
            "gcloud", "config", "set", "project", project_id
        ], capture_output=True, text=True, check=True)
        
        print("Project set successfully")
        
        # Verify authentication
        print("Verifying authentication...")
        result = subprocess.run([
            "gcloud", "auth", "list"
        ], capture_output=True, text=True, check=True)
        
        print("Current authenticated accounts:")
        print(result.stdout)
        
        # Test access to HMF bucket
        print("Testing access to HMF bucket...")
        result = subprocess.run([
            "gsutil", "-u", project_id, "ls", "gs://hmf-dr-141-update5/"
        ], capture_output=True, text=True, check=True)
        
        print("Successfully accessed HMF bucket")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"ERROR: Authentication failed: {e}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False
    except Exception as e:
        print(f"ERROR: Unexpected error during authentication: {e}")
        return False

def check_gcloud_installed():
    """Check if gcloud CLI is installed"""
    try:
        result = subprocess.run(["gcloud", "version"], 
                              capture_output=True, text=True, check=True)
        print("Google Cloud SDK is installed:")
        print(result.stdout)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("ERROR: Google Cloud SDK is not installed or not in PATH")
        print("Please install it from: https://cloud.google.com/sdk/docs/install")
        return False

def main():
    """Main function for command line usage"""
    if len(sys.argv) != 3:
        print("Usage: python gcp_auth.py <key_file_path> <project_id>")
        sys.exit(1)
    
    key_file_path = sys.argv[1]
    project_id = sys.argv[2]
    
    # Check if gcloud is installed
    if not check_gcloud_installed():
        sys.exit(1)
    
    # Authenticate
    if authenticate_gcp(key_file_path, project_id):
        print("Authentication completed successfully")
        sys.exit(0)
    else:
        print("Authentication failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
