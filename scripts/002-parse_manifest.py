# <PERSON> Bahcheli

import argparse, time, os
import json
import sys

help_message = '''
Parse HMF manifest.json file and select samples with RNA fastq files
'''

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Parse HMF manifest and select RNA-seq samples"
    )
    parser.add_argument(
        "--manifest",
        required=True,
        help="Path to manifest.json file"
    )
    parser.add_argument(
        "--output",
        required=True,
        help="Output path for selected samples JSON"
    )
    parser.add_argument(
        "--num_samples",
        type=int,
        default=20,
        help="Number of samples to select"
    )
    parser.add_argument(
        "--processed_samples",
        required=True,
        help="Path to processed samples tracking file"
    )

    return parser.parse_args()

def load_processed_samples(processed_samples_file):
    """
    Load list of already processed sample IDs

    Args:
        processed_samples_file (str): Path to processed samples file

    Returns:
        set: Set of processed sample IDs
    """

    if not os.path.exists(processed_samples_file):
        print(f"No processed samples file found: {processed_samples_file}")
        return set()

    try:
        with open(processed_samples_file, 'r') as f:
            processed = set(line.strip() for line in f if line.strip())
        print(f"Loaded {len(processed)} already processed samples")
        return processed
    except Exception as e:
        print(f"ERROR: Failed to load processed samples: {e}")
        return set()

def parse_manifest(manifest_file, processed_samples_file, num_samples=20):
    """
    Parse manifest.json and select unprocessed samples with RNA fastq files

    Args:
        manifest_file (str): Path to manifest.json file
        processed_samples_file (str): Path to processed samples tracking file
        num_samples (int): Number of samples to select

    Returns:
        dict: Selected samples with their RNA fastq file information
    """

    print(f"Parsing manifest file: {manifest_file}")

    # Load processed samples
    processed_samples = load_processed_samples(processed_samples_file)

    # Read manifest file
    try:
        with open(manifest_file, 'r') as f:
            manifest = json.load(f)
    except FileNotFoundError:
        print(f"ERROR: Manifest file not found: {manifest_file}")
        return None
    except json.JSONDecodeError as e:
        print(f"ERROR: Invalid JSON in manifest file: {e}")
        return None

    # Check if 'data' field exists
    if 'data' not in manifest:
        print("ERROR: 'data' field not found in manifest")
        return None
    
    samples_with_rna = {}
    total_samples = len(manifest['data'])
    print(f"Total samples in manifest: {total_samples}")
    
    # Iterate through samples to find unprocessed ones with RNA fastq files
    for sample_id, sample_data in manifest['data'].items():
        # Skip if already processed
        if sample_id in processed_samples:
            continue

        # Check if sample has RNA data
        if 'rna' in sample_data and 'fastq' in sample_data['rna']:
            rna_fastq = sample_data['rna']['fastq']

            # Check if there are actual fastq files
            if isinstance(rna_fastq, list) and len(rna_fastq) > 0:
                # Filter out any empty entries
                valid_fastq = [f for f in rna_fastq if f and 'url' in f]

                if valid_fastq:
                    samples_with_rna[sample_id] = {
                        'fastq_files': valid_fastq,
                        'sample_info': {
                            'sample_id': sample_id,
                            'patient_id': sample_data.get('patientId', 'unknown'),
                            'sample_type': sample_data.get('sampleType', 'unknown'),
                            'primary_tumor_location': sample_data.get('primaryTumorLocation', 'unknown')
                        }
                    }
    
    print(f"Found {len(samples_with_rna)} unprocessed samples with RNA fastq files")
    print(f"Already processed: {len(processed_samples)} samples")

    # Select the requested number of samples
    if len(samples_with_rna) == 0:
        print("No unprocessed samples found")
        return {}
    elif len(samples_with_rna) < num_samples:
        print(f"WARNING: Only {len(samples_with_rna)} samples available, less than requested {num_samples}")
        selected_samples = samples_with_rna
    else:
        # Select first N samples (you could randomize this if needed)
        sample_ids = list(samples_with_rna.keys())[:num_samples]
        selected_samples = {sid: samples_with_rna[sid] for sid in sample_ids}

    print(f"Selected {len(selected_samples)} samples for processing:")
    for sample_id, sample_info in selected_samples.items():
        num_files = len(sample_info['fastq_files'])
        print(f"  - {sample_id}: {num_files} FASTQ files")

    return selected_samples

def save_selected_samples(selected_samples, output_file):
    """
    Save selected samples to JSON file
    
    Args:
        selected_samples (dict): Selected samples data
        output_file (str): Output file path
    """
    
    try:
        with open(output_file, 'w') as f:
            json.dump(selected_samples, f, indent=2)
        print(f"Selected samples saved to: {output_file}")
        return True
    except Exception as e:
        print(f"ERROR: Failed to save selected samples: {e}")
        return False

def main():
    print('parse_manifest.py')
    t1 = time.time()

    # load arguments from path
    args = parse_arguments()

    # Parse manifest and select samples
    selected_samples = parse_manifest(args.manifest, args.processed_samples, args.num_samples)

    if selected_samples is None:
        print("ERROR: Failed to parse manifest")
        sys.exit(1)

    # Save selected samples
    if not save_selected_samples(selected_samples, args.output):
        sys.exit(1)

    print(round(time.time() - t1, 2))
    print('parse_manifest.py COMPLETE')

if __name__ == "__main__":
    main()
