#!/usr/bin/env python3
"""
Nextflow execution script for HMF RNA-seq pipeline
This script runs the Nextflow RNA-seq pipeline with the generated samplesheet
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_nextflow_installed():
    """Check if Nextflow is installed and accessible"""
    try:
        result = subprocess.run(['nextflow', '-version'], 
                              capture_output=True, text=True, check=True)
        print("Nextflow is installed:")
        print(result.stdout)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("ERROR: Nextflow is not installed or not in PATH")
        print("Please install Nextflow from: https://www.nextflow.io/docs/latest/getstarted.html")
        return False

def check_container_runtime():
    """Check if container runtime (Docker/Singularity) is available"""
    
    # Check for Docker
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, check=True)
        print("Docker is available:")
        print(result.stdout.strip())
        return 'docker'
    except (subprocess.CalledProcessError, FileNotFoundError):
        pass
    
    # Check for Singularity
    try:
        result = subprocess.run(['singularity', '--version'], 
                              capture_output=True, text=True, check=True)
        print("Singularity is available:")
        print(result.stdout.strip())
        return 'singularity'
    except (subprocess.CalledProcessError, FileNotFoundError):
        pass
    
    print("WARNING: Neither Docker nor Singularity found")
    print("Container runtime is recommended for reproducible results")
    return None

def prepare_reference_files(work_dir, reference_config):
    """
    Prepare reference genome files
    
    Args:
        work_dir (str): Working directory
        reference_config (dict): Reference genome configuration
    
    Returns:
        tuple: (fasta_path, gtf_path) or (None, None) if not found
    """
    
    ref_dir = os.path.join(work_dir, "reference")
    os.makedirs(ref_dir, exist_ok=True)
    
    fasta_file = reference_config.get('fasta')
    gtf_file = reference_config.get('gtf')
    
    fasta_path = None
    gtf_path = None
    
    # Check if reference files exist locally
    if fasta_file:
        local_fasta = os.path.join(ref_dir, fasta_file)
        if os.path.exists(local_fasta):
            fasta_path = local_fasta
            print(f"Found reference FASTA: {fasta_path}")
        else:
            print(f"WARNING: Reference FASTA not found: {local_fasta}")
    
    if gtf_file:
        local_gtf = os.path.join(ref_dir, gtf_file)
        if os.path.exists(local_gtf):
            gtf_path = local_gtf
            print(f"Found reference GTF: {gtf_path}")
        else:
            print(f"WARNING: Reference GTF not found: {local_gtf}")
    
    return fasta_path, gtf_path

def generate_nextflow_config(work_dir, container_runtime):
    """
    Generate Nextflow configuration file
    
    Args:
        work_dir (str): Working directory
        container_runtime (str): Container runtime (docker/singularity)
    
    Returns:
        str: Path to generated config file
    """
    
    config_file = os.path.join(work_dir, "nextflow.config")
    
    config_content = f"""
// Nextflow configuration for HMF RNA-seq processing

params {{
    // Maximum resource usage
    max_cpus = {os.cpu_count() or 8}
    max_memory = '32.GB'
    max_time = '240.h'
}}

process {{
    // Error handling
    errorStrategy = 'retry'
    maxRetries = 2
    
    // Resource defaults
    cpus = 2
    memory = '4.GB'
    time = '2.h'
}}
"""
    
    if container_runtime == 'docker':
        config_content += """
docker {
    enabled = true
    runOptions = '-u $(id -u):$(id -g)'
}
"""
    elif container_runtime == 'singularity':
        config_content += """
singularity {
    enabled = true
    autoMounts = true
}
"""
    
    config_content += """
// Cleanup work directory after successful completion
cleanup = true
"""
    
    with open(config_file, 'w') as f:
        f.write(config_content)
    
    print(f"Generated Nextflow config: {config_file}")
    return config_file

def run_nextflow_pipeline(samplesheet, work_dir, config, testing_mode=False):
    """
    Run the Nextflow RNA-seq pipeline
    
    Args:
        samplesheet (str): Path to samplesheet file
        work_dir (str): Working directory
        config (dict): Configuration dictionary
        testing_mode (bool): If True, skip actual Nextflow execution
    
    Returns:
        bool: True if successful, False otherwise
    """
    
    if testing_mode:
        print("TESTING MODE: Skipping Nextflow execution")
        print(f"Would run pipeline with samplesheet: {samplesheet}")
        return True
    
    # Check prerequisites
    if not check_nextflow_installed():
        return False
    
    container_runtime = check_container_runtime()
    
    # Prepare reference files
    fasta_path, gtf_path = prepare_reference_files(work_dir, config.get('reference_genome', {}))
    
    # Generate Nextflow config
    nextflow_config = generate_nextflow_config(work_dir, container_runtime)
    
    # Prepare output directory
    output_dir = os.path.join(work_dir, "results")
    os.makedirs(output_dir, exist_ok=True)
    
    # Build Nextflow command
    pipeline_path = config.get('nextflow_pipeline', 'nf-core/rnaseq')
    
    cmd = [
        'nextflow', 'run', pipeline_path,
        '--input', samplesheet,
        '--outdir', output_dir,
        '-c', nextflow_config
    ]
    
    # Add reference genome files if available
    if fasta_path:
        cmd.extend(['--fasta', fasta_path])
    if gtf_path:
        cmd.extend(['--gtf', gtf_path])
    
    # Add container profile
    if container_runtime:
        cmd.extend(['-profile', container_runtime])
    
    # Add resume option
    cmd.append('-resume')
    
    print("Running Nextflow pipeline:")
    print(" ".join(cmd))
    
    try:
        # Run Nextflow pipeline
        result = subprocess.run(cmd, cwd=work_dir, check=True)
        print("Nextflow pipeline completed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"ERROR: Nextflow pipeline failed with return code {e.returncode}")
        return False
    except Exception as e:
        print(f"ERROR: Unexpected error running Nextflow: {e}")
        return False

def main():
    """Main function for Snakemake script execution"""
    
    # Get parameters from Snakemake
    samplesheet = snakemake.input.samplesheet
    output_flag = snakemake.output.nextflow_complete
    work_dir = snakemake.config['work_dir']
    testing_mode = snakemake.config.get('testing_mode', False)
    
    # Run Nextflow pipeline
    success = run_nextflow_pipeline(samplesheet, work_dir, snakemake.config, testing_mode)
    
    if success:
        # Create completion flag
        with open(output_flag, 'w') as f:
            f.write("Nextflow pipeline completed successfully\n")
        print("Nextflow execution completed")
    else:
        print("ERROR: Nextflow pipeline failed")
        sys.exit(1)

if __name__ == "__main__":
    # For standalone testing
    if len(sys.argv) >= 3:
        samplesheet = sys.argv[1]
        work_dir = sys.argv[2]
        testing_mode = len(sys.argv) > 3 and sys.argv[3].lower() == 'true'
        
        config = {
            'nextflow_pipeline': 'nf-core/rnaseq',
            'reference_genome': {
                'fasta': 'GCF_000001405.40_GRCh38.p14_genomic.fna',
                'gtf': 'GCF_000001405.40_GRCh38.p14_genomic.gtf'
            }
        }
        
        success = run_nextflow_pipeline(samplesheet, work_dir, config, testing_mode)
        if not success:
            sys.exit(1)
    else:
        # Called from Snakemake
        main()
