# <PERSON> Ba<PERSON>cheli

import argparse, time, os
import json
import sys

help_message = '''
Update processed samples tracking file with newly processed sample IDs
'''

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Update processed samples tracking file"
    )
    parser.add_argument(
        "--selected_samples",
        required=True,
        help="Path to selected samples JSON file"
    )
    parser.add_argument(
        "--output",
        required=True,
        help="Path to processed samples tracking file"
    )

    return parser.parse_args()

def load_selected_samples(selected_samples_file):
    """
    Load selected samples from JSON file
    
    Args:
        selected_samples_file (str): Path to selected samples JSON
    
    Returns:
        dict: Selected samples data
    """
    
    try:
        with open(selected_samples_file, 'r') as f:
            selected_samples = json.load(f)
        print(f"Loaded {len(selected_samples)} selected samples")
        return selected_samples
    except Exception as e:
        print(f"ERROR: Failed to load selected samples: {e}")
        return {}

def load_existing_processed_samples(processed_samples_file):
    """
    Load existing processed samples
    
    Args:
        processed_samples_file (str): Path to processed samples file
    
    Returns:
        set: Set of already processed sample IDs
    """
    
    if not os.path.exists(processed_samples_file):
        return set()
    
    try:
        with open(processed_samples_file, 'r') as f:
            processed = set(line.strip() for line in f if line.strip())
        print(f"Found {len(processed)} already processed samples")
        return processed
    except Exception as e:
        print(f"ERROR: Failed to load existing processed samples: {e}")
        return set()

def update_processed_samples(selected_samples_file, processed_samples_file):
    """
    Update processed samples file with newly processed samples
    
    Args:
        selected_samples_file (str): Path to selected samples JSON
        processed_samples_file (str): Path to processed samples tracking file
    
    Returns:
        bool: True if successful
    """
    
    # Load selected samples
    selected_samples = load_selected_samples(selected_samples_file)
    
    if not selected_samples:
        print("No selected samples to process")
        return True
    
    # Load existing processed samples
    existing_processed = load_existing_processed_samples(processed_samples_file)
    
    # Get new sample IDs
    new_sample_ids = set(selected_samples.keys())
    
    # Combine with existing
    all_processed = existing_processed.union(new_sample_ids)
    
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(processed_samples_file), exist_ok=True)
    
    try:
        # Write updated processed samples file
        with open(processed_samples_file, 'w') as f:
            for sample_id in sorted(all_processed):
                f.write(f"{sample_id}\n")
        
        print(f"Updated processed samples file: {processed_samples_file}")
        print(f"  - Added {len(new_sample_ids)} new samples")
        print(f"  - Total processed samples: {len(all_processed)}")
        
        # Show newly added samples
        if new_sample_ids:
            print("Newly processed samples:")
            for sample_id in sorted(new_sample_ids):
                print(f"  - {sample_id}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to update processed samples file: {e}")
        return False

def main():
    print('update_processed_samples.py')
    t1 = time.time()

    # load arguments from path
    args = parse_arguments()

    # Update processed samples file
    success = update_processed_samples(args.selected_samples, args.output)
    
    if not success:
        print("ERROR: Failed to update processed samples")
        sys.exit(1)
    
    print(round(time.time() - t1, 2))
    print('update_processed_samples.py COMPLETE')

if __name__ == "__main__":
    main()
