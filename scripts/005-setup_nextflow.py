# Alec Bahcheli

import argparse, time, os
import sys
import subprocess
import shutil

help_message = '''
Setup Nextflow execution environment for HMF RNA-seq pipeline
'''

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Setup Nextflow execution environment"
    )
    parser.add_argument(
        "--work_dir",
        required=True,
        help="Working directory"
    )
    parser.add_argument(
        "--samplesheet",
        required=True,
        help="Path to samplesheet CSV file"
    )
    parser.add_argument(
        "--output",
        required=True,
        help="Output completion flag file"
    )

    return parser.parse_args()

def check_nextflow_installed():
    """Check if Nextflow is installed and accessible"""
    try:
        result = subprocess.run(['nextflow', '-version'], 
                              capture_output=True, text=True, check=True)
        print("✓ Nextflow is installed:")
        print(result.stdout.strip())
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ Nextflow is not installed or not in PATH")
        print("  Please install Nextflow from: https://www.nextflow.io/docs/latest/getstarted.html")
        return False

def check_container_runtime():
    """Check if container runtime (Docker/Singularity) is available"""
    
    # Check for Docker
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, check=True)
        print("✓ Docker is available:")
        print(f"  {result.stdout.strip()}")
        return 'docker'
    except (subprocess.CalledProcessError, FileNotFoundError):
        pass
    
    # Check for Singularity
    try:
        result = subprocess.run(['singularity', '--version'], 
                              capture_output=True, text=True, check=True)
        print("✓ Singularity is available:")
        print(f"  {result.stdout.strip()}")
        return 'singularity'
    except (subprocess.CalledProcessError, FileNotFoundError):
        pass
    
    print("⚠ Neither Docker nor Singularity found")
    print("  Container runtime is recommended for reproducible results")
    return None

def generate_nextflow_config(work_dir, container_runtime):
    """
    Generate Nextflow configuration file
    
    Args:
        work_dir (str): Working directory
        container_runtime (str): Container runtime (docker/singularity)
    
    Returns:
        str: Path to generated config file
    """
    
    # Create the nextflow_rnaseq directory
    nextflow_dir = "/Users/<USER>/Desktop/HMF_RNASeq_2025/nextflow_rnaseq"
    os.makedirs(nextflow_dir, exist_ok=True)

    config_file = os.path.join(nextflow_dir, "nextflow.config")
    
    config_content = f"""
// Nextflow configuration for HMF RNA-seq processing
// Generated automatically by setup_nextflow.py

params {{
    // Maximum resource usage
    max_cpus = {os.cpu_count() or 8}
    max_memory = '32.GB'
    max_time = '240.h'
    
    // Output directory
    outdir = './results'
    
    // Reference genome (update paths as needed)
    genome = 'GRCh38'
    // fasta = '/path/to/GRCh38.fasta'
    // gtf = '/path/to/GRCh38.gtf'
}}

process {{
    // Error handling
    errorStrategy = 'retry'
    maxRetries = 2
    
    // Resource defaults
    cpus = 2
    memory = '4.GB'
    time = '2.h'
    
    // Process-specific resource allocation
    withName: 'FASTQC' {{
        cpus = 2
        memory = '4.GB'
    }}
    
    withName: 'STAR_ALIGN' {{
        cpus = 8
        memory = '32.GB'
        time = '8.h'
    }}
    
    withName: 'FEATURECOUNTS' {{
        cpus = 4
        memory = '8.GB'
    }}
}}

// Timeline and execution reports
timeline {{
    enabled = true
    file = "${{params.outdir}}/pipeline_info/execution_timeline.html"
}}

report {{
    enabled = true
    file = "${{params.outdir}}/pipeline_info/execution_report.html"
}}

trace {{
    enabled = true
    file = "${{params.outdir}}/pipeline_info/execution_trace.txt"
}}

dag {{
    enabled = true
    file = "${{params.outdir}}/pipeline_info/pipeline_dag.svg"
}}
"""
    
    if container_runtime == 'docker':
        config_content += """
// Docker configuration
docker {
    enabled = true
    runOptions = '-u $(id -u):$(id -g)'
}

process {
    container = 'nfcore/rnaseq:3.15.0'
}
"""
    elif container_runtime == 'singularity':
        config_content += """
// Singularity configuration
singularity {
    enabled = true
    autoMounts = true
    cacheDir = './singularity_cache'
}

process {
    container = 'nfcore/rnaseq:3.15.0'
}
"""
    
    config_content += """
// Cleanup work directory after successful completion
cleanup = false  // Set to true for production runs
"""
    
    with open(config_file, 'w') as f:
        f.write(config_content)
    
    print(f"✓ Generated Nextflow config: {config_file}")
    return config_file

def generate_run_script(work_dir, samplesheet):
    """
    Generate shell script to run Nextflow pipeline at nextflow_rnaseq/rnaseq.sh

    Args:
        work_dir (str): Working directory
        samplesheet (str): Path to samplesheet

    Returns:
        str: Path to generated script
    """

    # Create the nextflow_rnaseq directory
    nextflow_dir = "/Users/<USER>/Desktop/HMF_RNASeq_2025/nextflow_rnaseq"
    os.makedirs(nextflow_dir, exist_ok=True)

    script_file = os.path.join(nextflow_dir, "rnaseq.sh")

    script_content = f"""#!/bin/bash
#$ -P reimandlab
#$ -N hmf_rnaseq
#$ -l h_vmem=10G,h_rt=7:0:0:0
#$ -q all.q
#$ -o {nextflow_dir}/
#$ -e {nextflow_dir}/

source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd {work_dir}

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export NXF_OFFLINE='true'

nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/nf-core-rnaseq_3.15.0/3_15_0 \\
    --input {samplesheet} \\
    --outdir {work_dir}/results \\
    --gtf /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/HMF_RNASeq_2025/data/ref_data/GCF_000001405.40_GRCh38.p14_genomic.gtf \\
    --fasta /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/HMF_RNASeq_2025/data/ref_data/GCF_000001405.40_GRCh38.p14_genomic.fna \\
    -profile singularity \\
    -c {nextflow_dir}/nextflow.config \\
    -resume
"""

    with open(script_file, 'w') as f:
        f.write(script_content)

    # Make script executable
    os.chmod(script_file, 0o755)

    print(f"✓ Generated run script: {script_file}")
    return script_file

def setup_nextflow_environment(work_dir, samplesheet):
    """
    Set up everything needed to run Nextflow

    Args:
        work_dir (str): Working directory
        samplesheet (str): Path to samplesheet

    Returns:
        bool: True if setup successful
    """

    print("Setting up Nextflow environment...")

    # Verify samplesheet exists
    if not os.path.exists(samplesheet):
        print(f"✗ Samplesheet not found: {samplesheet}")
        return False
    else:
        print(f"✓ Samplesheet found: {samplesheet}")

    # Create necessary directories
    os.makedirs(os.path.join(work_dir, "results"), exist_ok=True)

    # Generate configuration files
    config_file = generate_nextflow_config(work_dir, 'singularity')
    run_script = generate_run_script(work_dir, samplesheet)

    print("Nextflow setup completed!")
    print(f"Configuration file: {config_file}")
    print(f"Run script: {run_script}")

    return True

def main():
    print('setup_nextflow.py')
    t1 = time.time()

    # load arguments from path
    args = parse_arguments()

    # Setup Nextflow environment
    success = setup_nextflow_environment(args.work_dir, args.samplesheet)

    if success:
        # Create completion flag
        with open(args.output, 'w') as f:
            f.write("Nextflow setup completed successfully\n")
            f.write(f"Samplesheet: {args.samplesheet}\n")
            f.write(f"Working directory: {args.work_dir}\n")
        print("Nextflow setup completed successfully")
    else:
        print("ERROR: Nextflow setup failed")
        sys.exit(1)

    print(round(time.time() - t1, 2))
    print('setup_nextflow.py COMPLETE')

if __name__ == "__main__":
    main()
