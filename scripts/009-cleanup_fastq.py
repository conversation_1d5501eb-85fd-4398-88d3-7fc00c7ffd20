# Alec Bahcheli

import argparse, time, os
import sys
import shutil
import glob

help_message = '''
Clean up FASTQ files after processing to save disk space
'''

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Clean up FASTQ files after processing"
    )
    parser.add_argument(
        "--work_dir",
        required=True,
        help="Working directory containing FASTQ files"
    )
    parser.add_argument(
        "--output",
        required=True,
        help="Output completion flag file"
    )
    parser.add_argument(
        "--dry_run",
        action="store_true",
        help="Show what would be deleted without actually deleting"
    )

    return parser.parse_args()

def get_directory_size(directory):
    """
    Calculate total size of directory in MB
    
    Args:
        directory (str): Directory path
    
    Returns:
        float: Size in MB
    """
    
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(directory):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
    except Exception as e:
        print(f"WARNING: Error calculating directory size: {e}")
    
    return total_size / (1024 * 1024)  # Convert to MB

def list_fastq_files(work_dir):
    """
    List all FASTQ files in the working directory
    
    Args:
        work_dir (str): Working directory
    
    Returns:
        list: List of FASTQ file paths
    """
    
    fastq_dir = os.path.join(work_dir, "fastq")
    
    if not os.path.exists(fastq_dir):
        print(f"FASTQ directory not found: {fastq_dir}")
        return []
    
    fastq_files = []
    
    # Find all FASTQ files
    for ext in ['*.fastq.gz', '*.fq.gz', '*.fastq', '*.fq']:
        pattern = os.path.join(fastq_dir, "**", ext)
        fastq_files.extend(glob.glob(pattern, recursive=True))
    
    return sorted(fastq_files)

def cleanup_fastq_files(work_dir, dry_run=False):
    """
    Clean up FASTQ files after processing
    
    Args:
        work_dir (str): Working directory
        dry_run (bool): If True, only show what would be deleted
    
    Returns:
        bool: True if successful
    """
    
    fastq_dir = os.path.join(work_dir, "fastq")
    
    if not os.path.exists(fastq_dir):
        print(f"FASTQ directory not found: {fastq_dir}")
        return True  # Nothing to clean up
    
    # Calculate size before cleanup
    size_before = get_directory_size(fastq_dir)
    
    # List all FASTQ files
    fastq_files = list_fastq_files(work_dir)
    
    if not fastq_files:
        print("No FASTQ files found to clean up")
        return True
    
    print(f"Found {len(fastq_files)} FASTQ files to clean up")
    print(f"Total size: {size_before:.2f} MB ({size_before/1024:.2f} GB)")
    
    if dry_run:
        print("\nDRY RUN - Files that would be deleted:")
        for file_path in fastq_files:
            file_size = os.path.getsize(file_path) / (1024 * 1024)
            print(f"  - {file_path} ({file_size:.1f} MB)")
        return True
    
    # Show files to be deleted
    print("\nDeleting FASTQ files:")
    deleted_count = 0
    failed_count = 0
    
    for file_path in fastq_files:
        try:
            file_size = os.path.getsize(file_path) / (1024 * 1024)
            os.remove(file_path)
            print(f"  ✓ Deleted: {os.path.basename(file_path)} ({file_size:.1f} MB)")
            deleted_count += 1
        except Exception as e:
            print(f"  ✗ Failed to delete {file_path}: {e}")
            failed_count += 1
    
    # Remove empty directories
    try:
        for root, dirs, files in os.walk(fastq_dir, topdown=False):
            for dir_name in dirs:
                dir_path = os.path.join(root, dir_name)
                try:
                    if not os.listdir(dir_path):  # Directory is empty
                        os.rmdir(dir_path)
                        print(f"  ✓ Removed empty directory: {dir_path}")
                except Exception as e:
                    print(f"  ⚠ Could not remove directory {dir_path}: {e}")
        
        # Remove main fastq directory if empty
        if os.path.exists(fastq_dir) and not os.listdir(fastq_dir):
            os.rmdir(fastq_dir)
            print(f"  ✓ Removed empty FASTQ directory: {fastq_dir}")
            
    except Exception as e:
        print(f"WARNING: Error cleaning up directories: {e}")
    
    print(f"\nCleanup summary:")
    print(f"  - Files deleted: {deleted_count}")
    print(f"  - Files failed: {failed_count}")
    print(f"  - Space freed: {size_before:.2f} MB ({size_before/1024:.2f} GB)")
    
    return failed_count == 0

def create_cleanup_summary(work_dir):
    """
    Create a summary file of the cleanup operation
    
    Args:
        work_dir (str): Working directory
    """
    
    summary_file = os.path.join(work_dir, "cleanup_summary.txt")
    
    try:
        with open(summary_file, 'w') as f:
            f.write("FASTQ Cleanup Summary\n")
            f.write("=" * 30 + "\n")
            f.write(f"Cleanup date: {os.popen('date').read().strip()}\n")
            f.write(f"Working directory: {work_dir}\n")
            
            # Check if fastq directory still exists
            fastq_dir = os.path.join(work_dir, "fastq")
            if os.path.exists(fastq_dir):
                remaining_files = list_fastq_files(work_dir)
                remaining_size = get_directory_size(fastq_dir)
                f.write(f"Remaining FASTQ files: {len(remaining_files)}\n")
                f.write(f"Remaining size: {remaining_size:.2f} MB\n")
            else:
                f.write("All FASTQ files and directories removed\n")
            
            f.write("\nCleanup completed successfully\n")
        
        print(f"✓ Created cleanup summary: {summary_file}")
        
    except Exception as e:
        print(f"WARNING: Could not create cleanup summary: {e}")

def main():
    print('cleanup_fastq.py')
    t1 = time.time()

    # load arguments from path
    args = parse_arguments()

    print("Starting FASTQ cleanup...")

    # Perform cleanup
    success = cleanup_fastq_files(args.work_dir, args.dry_run)

    if success:
        if not args.dry_run:
            create_cleanup_summary(args.work_dir)

        # Create completion flag
        with open(args.output, 'w') as f:
            f.write("FASTQ cleanup completed successfully\n")
            f.write(f"Working directory: {args.work_dir}\n")
            f.write(f"Dry run: {args.dry_run}\n")

        print("FASTQ cleanup completed successfully")
    else:
        print("ERROR: FASTQ cleanup failed")
        sys.exit(1)

    print(round(time.time() - t1, 2))
    print('cleanup_fastq.py COMPLETE')

if __name__ == "__main__":
    main()
