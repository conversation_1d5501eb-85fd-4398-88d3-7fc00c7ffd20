# Alec Bahcheli
# Snakemake workflow for RNA-seq FASTQ download from GC and pre-processing

###########################
# Environment paths
###########################

# conda envs
PYTHON = '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/bin/python'
RSCRIPT = '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript'

###########################
# Configuration
###########################

import os

# main project directory
WORK_DIR = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/HMF_RNASeq_2025'

# sub directories
BIN_DIR = "/".join([WORK_DIR, "bin"])
DATA_DIR = "/".join([WORK_DIR, "data"])
REF_DATA_DIR = "/".join([WORK_DIR, "data", "ref_data"])
RES_DIR = "/".join([WORK_DIR, "results"])

# Global variables
MANIFEST_FILE = WORK_DIR + "/data/manifest.json"
GCP_KEY_FILE = WORK_DIR + "/gcp_key.json"
PROJECT_ID = "reimandlab"
NUM_SAMPLES = 20

# Create output directories
os.makedirs(f"{DATA_DIR}", exist_ok=True)
os.makedirs(f"{DATA_DIR}/logs", exist_ok=True)
os.makedirs(f"{REF_DATA_DIR}", exist_ok=True)
os.makedirs(f"{RES_DIR}", exist_ok=True)

###########################
# Rules
###########################

# Rule to run the entire workflow
rule all:
    input:
        f"{RES_DIR}/master_samplesheet.csv"


###################################
# Include workflow modules
###################################
include: "snakemake/001-gc_rnaseq_processing.smk"
