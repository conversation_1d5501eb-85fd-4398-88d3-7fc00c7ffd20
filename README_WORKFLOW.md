# HMF RNA-seq Processing Workflow

This Snakemake workflow automates the download and processing of RNA-seq FASTQ files from Hartwig Medical Foundation's Google Cloud Platform.

## Workflow Overview

The workflow consists of the following steps:

1. **GCP Authentication** (`gcp_auth`) - Authenticate with Google Cloud Platform using service account key
2. **Manifest Parsing** (`parse_manifest`) - Parse manifest.json and select samples with RNA fastq files
3. **FASTQ Download** (`download_fastq`) - Download FASTQ files from Google Cloud Storage
4. **Samplesheet Generation** (`generate_samplesheet`) - Create Nextflow-compatible samplesheet
5. **Nextflow Setup** (`setup_nextflow`) - Prepare Nextflow execution environment
6. **Master Tracking** (`update_master_samplesheet`) - Update master sample tracking sheet
7. **Cleanup** (`cleanup_fastq`) - Optional cleanup of raw FASTQ files

## Files Structure

```
├── Snakefile                           # Main Snakemake workflow
├── snakemake/001-gc_rnaseq_processing.smk  # Alternative Snakemake file
├── scripts/
│   ├── parse_manifest.py              # Parse HMF manifest
│   ├── download_fastq.py               # Download FASTQ files
│   ├── generate_samplesheet.py         # Generate Nextflow samplesheet
│   ├── setup_nextflow.py               # Setup Nextflow environment
│   ├── update_master_samplesheet.py    # Update master tracking
│   └── cleanup_fastq.py                # Clean up FASTQ files
└── README_WORKFLOW.md                  # This file
```

## Prerequisites

1. **Google Cloud SDK** - Install and configure gcloud CLI
2. **Service Account Key** - Obtain JSON key file from Hartwig Medical Foundation
3. **Python Environment** - Python 3.7+ with required packages
4. **Snakemake** - Workflow management system
5. **Nextflow** (optional) - For running RNA-seq pipeline

## Configuration

Edit the configuration variables in `Snakefile`:

```python
# main project directory
WORK_DIR = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/HMF_RNASeq_2025'

# Global variables
MANIFEST_FILE = WORK_DIR + "/data/manifest.json"
GCP_KEY_FILE = WORK_DIR + "/gcp_key.json"
PROJECT_ID = "your-gcp-project-id"
NUM_SAMPLES = 20
```

## Usage

### 1. Prepare Environment

```bash
# Place your service account key file
cp /path/to/your/service-account-key.json gcp_key.json

# Place the HMF manifest file
cp /path/to/manifest.json data/manifest.json
```

### 2. Run Workflow

```bash
# Dry run to check workflow
snakemake -n

# Run with 4 cores
snakemake -j 4

# Run specific rule
snakemake parse_manifest -j 1
```

### 3. Monitor Progress

Check log files in `data/logs/` directory:
- `gcp_auth.log` - Authentication logs
- `download_fastq.log` - Download progress
- `generate_samplesheet.log` - Samplesheet generation
- `nextflow_setup.log` - Nextflow setup logs

## Output Files

- `data/selected_samples.json` - Selected samples for processing
- `data/samplesheet.csv` - Nextflow input samplesheet
- `data/nextflow_setup_complete.txt` - Nextflow setup completion flag
- `results/master_samplesheet.csv` - Master tracking of all processed samples
- `data/fastq/` - Downloaded FASTQ files (cleaned up after processing)

## Script Arguments

Each Python script follows the same argument pattern:

### parse_manifest.py
```bash
python scripts/parse_manifest.py --manifest manifest.json --output selected_samples.json --num_samples 20
```

### download_fastq.py
```bash
python scripts/download_fastq.py --selected_samples selected_samples.json --work_dir data --project_id PROJECT_ID --output download_complete.txt
```

### generate_samplesheet.py
```bash
python scripts/generate_samplesheet.py --work_dir data --output samplesheet.csv
```

### setup_nextflow.py
```bash
python scripts/setup_nextflow.py --work_dir data --samplesheet samplesheet.csv --output nextflow_setup_complete.txt
```

### update_master_samplesheet.py
```bash
python scripts/update_master_samplesheet.py --master_file master_samplesheet.csv --selected_samples selected_samples.json --samplesheet samplesheet.csv
```

### cleanup_fastq.py
```bash
python scripts/cleanup_fastq.py --work_dir data --output cleanup_complete.txt [--dry_run]
```

## Master Sample Sheet

The master sample sheet (`results/master_samplesheet.csv`) tracks all processed samples with the following columns:

- `batch_id` - Unique batch identifier
- `processing_date` - Date of processing
- `sample_id` - HMF sample identifier
- `patient_id` - Patient identifier
- `sample_type` - Sample type (tumor/normal)
- `primary_tumor_location` - Tumor location
- `num_fastq_files` - Number of FASTQ files
- `num_lanes` - Number of sequencing lanes
- `total_size_mb` - Total data size in MB
- `download_status` - Download completion status
- `nextflow_status` - Nextflow preparation status
- `notes` - Additional notes

## Troubleshooting

1. **Authentication Issues**: Ensure service account key is valid and has proper permissions
2. **Download Failures**: Check network connectivity and GCP quotas
3. **Missing Files**: Verify manifest.json format and file paths
4. **Permission Errors**: Ensure write permissions to output directories

## Notes

- The workflow is designed for batch processing of multiple samples
- FASTQ files are automatically cleaned up after processing to save disk space
- All processing is logged and tracked in the master sample sheet
- The workflow prepares Nextflow execution but doesn't run it automatically
