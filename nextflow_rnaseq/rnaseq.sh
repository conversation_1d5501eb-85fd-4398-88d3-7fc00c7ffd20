#!/bin/bash
#$ -P reimandlab
#$ -N singh_rnaseq
#$ -l h_vmem=10G,h_rt=7:0:0:0
#$ -q all.q
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/rnaseq/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/rnaseq/


source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/singh_lab_sample_processing/results/2025_07_09/rnaseq

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export NXF_OFFLINE='true'


nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/nf-core-rnaseq_3.15.0/3_15_0 --input /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/singh_lab_sample_processing/bin/2025_07_09/rnaseq/rnaseq_input.csv --outdir /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/singh_lab_sample_processing/results/2025_07_09/rnaseq --gtf /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/singh_lab_sample_processing/results/2025_07_09/rnaseq/GCF_000001405.40_GRCh38.p14_genomic.gtf --fasta /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/singh_lab_sample_processing/results/2025_07_09/rnaseq/GCF_000001405.40_GRCh38.p14_genomic.fna -profile singularity -c /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/singh_lab_sample_processing/bin/2025_07_09/rnaseq/nextflow.config -resume

