# Alec <PERSON>
# Snakemake workflow for HMF RNA-seq data download and processing
# Processes samples in batches of 20 with tracking

###########################
# Rules
###########################

# Rule to run one complete batch
rule all:
    input:
        f"{DATA_DIR}/processed_samples.txt"

# Rule to authenticate with Google Cloud Platform
rule gcp_auth:
    input:
        key_file=GCP_KEY_FILE

    output:
        auth_flag=f"{DATA_DIR}/gcp_auth_complete.txt"

    log:
        f"{DATA_DIR}/logs/gcp_auth.log"

    shell:
        """
        # Authenticate with service account
        gcloud auth activate-service-account --key-file={input.key_file} 2>&1 | tee {log}

        # Set project
        gcloud config set project {PROJECT_ID} 2>&1 | tee -a {log}

        # Verify authentication
        gcloud auth list 2>&1 | tee -a {log}

        # Create completion flag
        touch {output.auth_flag}
        """

# Rule to parse manifest and select next unprocessed samples
rule parse_manifest:
    input:
        manifest=MANIFEST_FILE,
        auth_flag=f"{DATA_DIR}/gcp_auth_complete.txt"

    output:
        selected_samples=f"{DATA_DIR}/selected_samples.json"

    resources:
        threads=1,
        queue="all.q",
        jobtime='1:0:0:0',
        individual_core_memory='10G'

    shell:
        "{PYTHON} scripts/parse_manifest.py --manifest {input.manifest} --output {output.selected_samples} --num_samples {NUM_SAMPLES} --processed_samples {DATA_DIR}/processed_samples.txt"

# Rule to download FASTQ files
rule download_fastq:
    input:
        selected_samples=f"{DATA_DIR}/selected_samples.json",
        auth_flag=f"{DATA_DIR}/gcp_auth_complete.txt"

    output:
        download_complete=f"{DATA_DIR}/download_complete.txt"
        
    resources:
        threads=1,
        queue="all.q",
        jobtime='12:0:0:0',
        individual_core_memory='8G'

    shell:
        "{PYTHON} scripts/download_fastq.py --selected_samples {input.selected_samples} --work_dir {DATA_DIR} --project_id {PROJECT_ID} --output {output.download_complete} --threads {resources.threads}"

# Rule to generate Nextflow samplesheet
rule generate_samplesheet:
    input:
        download_complete=f"{DATA_DIR}/download_complete.txt"

    output:
        samplesheet=f"{DATA_DIR}/samplesheet.csv"

    resources:
        threads=1,
        queue="all.q",
        jobtime='1:0:0:0',
        individual_core_memory='4G'
        
    shell:
        "{PYTHON} scripts/generate_samplesheet.py --work_dir {DATA_DIR} --output {output.samplesheet}"

# Rule to setup Nextflow execution
rule setup_nextflow:
    input:
        samplesheet=f"{DATA_DIR}/samplesheet.csv"

    output:
        nextflow_setup=f"{DATA_DIR}/nextflow_setup_complete.txt"

    resources:
        threads=1,
        queue="all.q",
        jobtime='1:0:0:0',
        individual_core_memory='4G'

    shell:
        "{PYTHON} scripts/setup_nextflow.py --work_dir {DATA_DIR} --samplesheet {input.samplesheet} --output {output.nextflow_setup}"

# Rule to update master samplesheet
rule update_master_samplesheet:
    input:
        samplesheet=f"{DATA_DIR}/samplesheet.csv",
        selected_samples=f"{DATA_DIR}/selected_samples.json",
        nextflow_setup=f"{DATA_DIR}/nextflow_setup_complete.txt"

    output:
        master_samplesheet=f"{RES_DIR}/master_samplesheet.csv"

    resources:
        threads=1,
        queue="all.q",
        jobtime='1:0:0:0',
        individual_core_memory='4G'

    shell:
        "{PYTHON} scripts/update_master_samplesheet.py --master_file {output.master_samplesheet} --selected_samples {input.selected_samples} --samplesheet {input.samplesheet}"

# Rule to update processed samples tracking
rule update_processed_samples:
    input:
        master_samplesheet=f"{RES_DIR}/master_samplesheet.csv",
        selected_samples=f"{DATA_DIR}/selected_samples.json"

    output:
        processed_samples=f"{DATA_DIR}/processed_samples.txt"

    resources:
        threads=1,
        queue="all.q",
        jobtime='1:0:0:0',
        individual_core_memory='4G'

    shell:
        "{PYTHON} scripts/update_processed_samples.py --selected_samples {input.selected_samples} --output {output.processed_samples}"

# Rule to cleanup FASTQ files (optional)
rule cleanup_fastq:
    input:
        processed_samples=f"{DATA_DIR}/processed_samples.txt"

    output:
        cleanup_complete=f"{DATA_DIR}/cleanup_complete.txt"

    resources:
        threads=1,
        queue="all.q",
        jobtime='1:0:0:0',
        individual_core_memory='4G'

    shell:
        "{PYTHON} scripts/cleanup_fastq.py --work_dir {DATA_DIR} --output {output.cleanup_complete}"
