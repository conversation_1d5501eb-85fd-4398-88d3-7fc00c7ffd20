# Alec Bahcheli
# Snakemake workflow for HMF RNA-seq data download and processing
# Processes samples in batches of 20 with tracking

###########################
# Rules
###########################

# Rule to run one complete batch
rule all:
    input:
        f"{DATA_DIR}/processed_samples.txt"

# Rule to authenticate with Google Cloud Platform
rule gcp_auth:
    input:
        key_file=GCP_KEY_FILE

    output:
        auth_flag=f"{DATA_DIR}/gcp_auth_complete.txt"

    log:
        f"{DATA_DIR}/logs/gcp_auth.log"

    shell:
        """
        # Activate gc_utils conda environment and authenticate
        source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
        conda activate /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/gc_utils

        # Authenticate with service account
        gcloud auth activate-service-account --key-file={input.key_file} 2>&1 | tee {log}

        # Set project
        gcloud config set project {PROJECT_ID} 2>&1 | tee -a {log}

        # Verify authentication
        gcloud auth list 2>&1 | tee -a {log}

        # Create completion flag
        touch {output.auth_flag}
        """

# Rule to parse manifest and select next unprocessed samples
rule parse_manifest:
    input:
        manifest=MANIFEST_FILE,
        auth_flag=f"{DATA_DIR}/gcp_auth_complete.txt"

    output:
        selected_samples=f"{DATA_DIR}/selected_samples.json"

    resources:
        threads=1,
        queue="all.q",
        jobtime='1:0:0:0',
        individual_core_memory='10G'

    shell:
        "{PYTHON} scripts/002-parse_manifest.py --manifest {input.manifest} --output {output.selected_samples} --num_samples {NUM_SAMPLES} --processed_samples {DATA_DIR}/processed_samples.txt"

# Rule to download FASTQ files
rule download_fastq:
    input:
        selected_samples=f"{DATA_DIR}/selected_samples.json",
        auth_flag=f"{DATA_DIR}/gcp_auth_complete.txt"

    output:
        download_complete=f"{DATA_DIR}/download_complete.txt"
        
    resources:
        threads=1,
        queue="all.q",
        jobtime='12:0:0:0',
        individual_core_memory='8G'

    shell:
        "{PYTHON} scripts/003-download_fastq.py --selected_samples {input.selected_samples} --work_dir {DATA_DIR} --project_id {PROJECT_ID} --output {output.download_complete} --threads {resources.threads}"

# Rule to generate Nextflow samplesheet
rule generate_samplesheet:
    input:
        download_complete=f"{DATA_DIR}/download_complete.txt"

    output:
        samplesheet=f"{DATA_DIR}/samplesheet.csv"

    resources:
        threads=1,
        queue="all.q",
        jobtime='1:0:0:0',
        individual_core_memory='4G'
        
    shell:
        "{PYTHON} scripts/004-generate_samplesheet.py --work_dir {DATA_DIR} --output {output.samplesheet}"

# Rule to setup Nextflow execution
rule setup_nextflow:
    input:
        samplesheet=f"{DATA_DIR}/samplesheet.csv"

    output:
        nextflow_setup=f"{DATA_DIR}/nextflow_setup_complete.txt"

    resources:
        threads=1,
        queue="all.q",
        jobtime='1:0:0:0',
        individual_core_memory='4G'

    shell:
        "{PYTHON} scripts/005-setup_nextflow.py --work_dir {DATA_DIR} --samplesheet {input.samplesheet} --output {output.nextflow_setup}"

# Rule to run Nextflow pipeline
rule run_nextflow:
    input:
        nextflow_setup=f"{DATA_DIR}/nextflow_setup_complete.txt"

    output:
        nextflow_complete=f"{DATA_DIR}/nextflow_complete.txt"

    resources:
        threads=1,
        queue="all.q",
        jobtime='7:0:0:0',
        individual_core_memory='10G'

    shell:
        """
        # Submit the Nextflow job and wait for completion
        qsub -sync y /Users/<USER>/Desktop/HMF_RNASeq_2025/nextflow_rnaseq/rnaseq.sh

        # Create completion flag
        touch {output.nextflow_complete}
        """

# Rule to update master samplesheet
rule update_master_samplesheet:
    input:
        samplesheet=f"{DATA_DIR}/samplesheet.csv",
        selected_samples=f"{DATA_DIR}/selected_samples.json",
        nextflow_complete=f"{DATA_DIR}/nextflow_complete.txt"

    output:
        master_samplesheet=f"{RES_DIR}/master_samplesheet.csv"

    resources:
        threads=1,
        queue="all.q",
        jobtime='1:0:0:0',
        individual_core_memory='4G'

    shell:
        "{PYTHON} scripts/007-update_master_samplesheet.py --master_file {output.master_samplesheet} --selected_samples {input.selected_samples} --samplesheet {input.samplesheet}"

# Rule to update processed samples tracking
rule update_processed_samples:
    input:
        master_samplesheet=f"{RES_DIR}/master_samplesheet.csv",
        selected_samples=f"{DATA_DIR}/selected_samples.json"

    output:
        processed_samples=f"{DATA_DIR}/processed_samples.txt"

    resources:
        threads=1,
        queue="all.q",
        jobtime='1:0:0:0',
        individual_core_memory='4G'

    shell:
        "{PYTHON} scripts/008-update_processed_samples.py --selected_samples {input.selected_samples} --output {output.processed_samples}"

# Rule to cleanup FASTQ files (optional)
rule cleanup_fastq:
    input:
        processed_samples=f"{DATA_DIR}/processed_samples.txt"

    output:
        cleanup_complete=f"{DATA_DIR}/cleanup_complete.txt"

    resources:
        threads=1,
        queue="all.q",
        jobtime='1:0:0:0',
        individual_core_memory='4G'

    shell:
        "{PYTHON} scripts/009-cleanup_fastq.py --work_dir {DATA_DIR} --output {output.cleanup_complete}"
