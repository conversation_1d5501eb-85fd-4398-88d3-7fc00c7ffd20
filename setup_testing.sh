#!/bin/bash
# Setup script for HMF RNA-seq processing testing environment

set -euo pipefail

# Configuration
TESTING_DIR="/Users/<USER>/Desktop/hmf_testing"
REPO_DIR="/Users/<USER>/Desktop/HMF_RNASeq_2025"

echo "Setting up HMF RNA-seq testing environment..."
echo "Testing directory: $TESTING_DIR"
echo "Repository directory: $REPO_DIR"

# Create testing directory structure
echo "Creating directory structure..."
mkdir -p "$TESTING_DIR"
mkdir -p "$TESTING_DIR/logs"
mkdir -p "$TESTING_DIR/results"
mkdir -p "$TESTING_DIR/reference"

# Copy configuration files
echo "Copying configuration files..."
cp "$REPO_DIR/config/config.yaml" "$TESTING_DIR/"

# Update config file for testing
echo "Updating configuration for testing..."
sed -i '' "s|work_dir: \"/Users/<USER>/Desktop/hmf_testing\"|work_dir: \"$TESTING_DIR\"|g" "$TESTING_DIR/config.yaml"

# Create a sample manifest.json for testing (if it doesn't exist)
if [ ! -f "$TESTING_DIR/manifest.json" ]; then
    echo "Creating sample manifest.json for testing..."
    cat > "$TESTING_DIR/manifest.json" << 'EOF'
{
  "data": {
    "SAMPLE001": {
      "patientId": "PATIENT001",
      "sampleType": "tumor",
      "primaryTumorLocation": "lung",
      "rna": {
        "fastq": [
          {
            "url": "gs://hmf-dr-141-update5/sample001_R1.fastq.gz",
            "size": **********
          },
          {
            "url": "gs://hmf-dr-141-update5/sample001_R2.fastq.gz",
            "size": **********
          }
        ]
      }
    },
    "SAMPLE002": {
      "patientId": "PATIENT002",
      "sampleType": "normal",
      "primaryTumorLocation": "breast",
      "rna": {
        "fastq": [
          {
            "url": "gs://hmf-dr-141-update5/sample002_R1.fastq.gz",
            "size": 800000000
          },
          {
            "url": "gs://hmf-dr-141-update5/sample002_R2.fastq.gz",
            "size": 800000000
          }
        ]
      }
    }
  }
}
EOF
fi

# Create a test script to run individual components
echo "Creating test script..."
cat > "$TESTING_DIR/test_components.sh" << 'EOF'
#!/bin/bash
# Test individual components of the HMF RNA-seq pipeline

set -euo pipefail

REPO_DIR="/Users/<USER>/Desktop/HMF_RNASeq_2025"
TESTING_DIR="/Users/<USER>/Desktop/hmf_testing"

cd "$TESTING_DIR"

echo "Testing HMF RNA-seq pipeline components..."

# Test 1: Parse manifest
echo "1. Testing manifest parsing..."
python3 "$REPO_DIR/scripts/parse_manifest.py" manifest.json selected_samples.json

if [ -f "selected_samples.json" ]; then
    echo "   ✓ Manifest parsing successful"
    echo "   Selected samples:"
    python3 -c "import json; data=json.load(open('selected_samples.json')); print(f'   Found {len(data)} samples')"
else
    echo "   ✗ Manifest parsing failed"
    exit 1
fi

# Test 2: Generate samplesheet (without actual files)
echo "2. Testing samplesheet generation..."
mkdir -p fastq/SAMPLE001 fastq/SAMPLE002

# Create dummy FASTQ files for testing
touch fastq/SAMPLE001/sample001_R1.fastq.gz
touch fastq/SAMPLE001/sample001_R2.fastq.gz
touch fastq/SAMPLE002/sample002_R1.fastq.gz
touch fastq/SAMPLE002/sample002_R2.fastq.gz

python3 "$REPO_DIR/scripts/generate_samplesheet.py" "$TESTING_DIR" samplesheet.csv

if [ -f "samplesheet.csv" ]; then
    echo "   ✓ Samplesheet generation successful"
    echo "   Samplesheet contents:"
    head -5 samplesheet.csv | sed 's/^/   /'
else
    echo "   ✗ Samplesheet generation failed"
    exit 1
fi

# Test 3: Setup Nextflow
echo "3. Testing Nextflow setup..."
python3 "$REPO_DIR/scripts/setup_nextflow.py" "$TESTING_DIR" samplesheet.csv

if [ -f "nextflow.config" ] && [ -f "run_nextflow.sh" ]; then
    echo "   ✓ Nextflow setup successful"
    echo "   Generated files:"
    echo "   - nextflow.config"
    echo "   - run_nextflow.sh"
else
    echo "   ✗ Nextflow setup failed"
    exit 1
fi

# Test 4: Update master samplesheet
echo "4. Testing master samplesheet update..."
python3 "$REPO_DIR/scripts/update_master_samplesheet.py" master_samplesheet.csv selected_samples.json samplesheet.csv

if [ -f "master_samplesheet.csv" ]; then
    echo "   ✓ Master samplesheet update successful"
    echo "   Master samplesheet summary:"
    wc -l master_samplesheet.csv | sed 's/^/   /'
else
    echo "   ✗ Master samplesheet update failed"
    exit 1
fi

# Test 5: Cleanup (dry run)
echo "5. Testing cleanup (dry run)..."
python3 "$REPO_DIR/scripts/cleanup_fastq.py" "$TESTING_DIR" true

echo ""
echo "All component tests completed successfully!"
echo "You can now run the full Snakemake workflow with:"
echo "  cd $TESTING_DIR"
echo "  snakemake -s $REPO_DIR/Snakefile --configfile config.yaml -n"
EOF

chmod +x "$TESTING_DIR/test_components.sh"

# Create instructions file
echo "Creating instructions..."
cat > "$TESTING_DIR/README_TESTING.md" << 'EOF'
# HMF RNA-seq Testing Environment

This directory contains a testing environment for the HMF RNA-seq processing pipeline.

## Files Created

- `config.yaml` - Configuration file for the pipeline
- `manifest.json` - Sample manifest file for testing
- `test_components.sh` - Script to test individual components
- `README_TESTING.md` - This file

## Testing Steps

### 1. Test Individual Components
Run the component test script:
```bash
cd /Users/<USER>/Desktop/hmf_testing
./test_components.sh
```

### 2. Test Full Snakemake Workflow (Dry Run)
Test the complete workflow without actually running it:
```bash
cd /Users/<USER>/Desktop/hmf_testing
snakemake -s /Users/<USER>/Desktop/HMF_RNASeq_2025/Snakefile --configfile config.yaml -n
```

### 3. Run Full Workflow
Once you have:
- Updated `config.yaml` with your GCP project ID and key file path
- Placed the real `manifest.json` file in this directory

Run the complete workflow:
```bash
cd /Users/<USER>/Desktop/hmf_testing
snakemake -s /Users/<USER>/Desktop/HMF_RNASeq_2025/Snakefile --configfile config.yaml -j 4
```

## Configuration

Before running with real data, update `config.yaml`:

1. Set your GCP project ID:
   ```yaml
   project_id: "your-actual-project-id"
   ```

2. Set path to your service account key:
   ```yaml
   gcp_key_file: "/path/to/your/service-account-key.json"
   ```

3. Place the real `manifest.json` file in this directory

## Output Files

The workflow will create:
- `selected_samples.json` - Selected samples for processing
- `samplesheet.csv` - Nextflow input samplesheet
- `master_samplesheet.csv` - Master record of all processed samples
- `nextflow.config` - Nextflow configuration
- `run_nextflow.sh` - Script to run Nextflow pipeline
- `fastq/` - Downloaded FASTQ files (deleted after processing)
- `results/` - Nextflow pipeline results
- `logs/` - Processing logs

## Notes

- The pipeline is set to testing mode by default (won't run Nextflow)
- FASTQ files are automatically cleaned up after processing
- All processing is tracked in the master samplesheet
EOF

echo ""
echo "✓ Testing environment setup complete!"
echo ""
echo "Testing directory: $TESTING_DIR"
echo "Next steps:"
echo "1. cd $TESTING_DIR"
echo "2. ./test_components.sh"
echo "3. Review README_TESTING.md for full instructions"
echo ""
echo "To run with real data:"
echo "1. Update config.yaml with your GCP project ID and key file"
echo "2. Place real manifest.json in $TESTING_DIR"
echo "3. Run: snakemake -s $REPO_DIR/Snakefile --configfile config.yaml -j 4"
