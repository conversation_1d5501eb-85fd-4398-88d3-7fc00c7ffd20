# Alec Bahcheli

import argparse, time, os
import json
import csv

help_message = '''
Check progress of HMF RNA-seq batch processing
'''

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Check progress of HMF RNA-seq batch processing"
    )
    parser.add_argument(
        "--work_dir",
        default="/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/HMF_RNASeq_2025",
        help="Working directory"
    )

    return parser.parse_args()

def count_total_samples(manifest_file):
    """Count total samples with RNA data in manifest"""
    
    if not os.path.exists(manifest_file):
        return 0
    
    try:
        with open(manifest_file, 'r') as f:
            manifest = json.load(f)
        
        if 'data' not in manifest:
            return 0
        
        count = 0
        for sample_id, sample_data in manifest['data'].items():
            if 'rna' in sample_data and 'fastq' in sample_data['rna']:
                rna_fastq = sample_data['rna']['fastq']
                if isinstance(rna_fastq, list) and len(rna_fastq) > 0:
                    valid_fastq = [f for f in rna_fastq if f and 'url' in f]
                    if valid_fastq:
                        count += 1
        
        return count
        
    except Exception as e:
        print(f"Error reading manifest: {e}")
        return 0

def count_processed_samples(processed_file):
    """Count processed samples"""
    
    if not os.path.exists(processed_file):
        return 0
    
    try:
        with open(processed_file, 'r') as f:
            lines = [line.strip() for line in f if line.strip()]
        return len(lines)
    except Exception as e:
        print(f"Error reading processed samples: {e}")
        return 0

def count_master_samplesheet_entries(master_file):
    """Count entries in master samplesheet"""
    
    if not os.path.exists(master_file):
        return 0
    
    try:
        with open(master_file, 'r', newline='') as f:
            reader = csv.reader(f)
            next(reader, None)  # Skip header
            return sum(1 for row in reader)
    except Exception as e:
        print(f"Error reading master samplesheet: {e}")
        return 0

def get_current_batch_info(work_dir):
    """Get information about current batch"""
    
    selected_file = os.path.join(work_dir, "data", "selected_samples.json")
    
    if not os.path.exists(selected_file):
        return 0, []
    
    try:
        with open(selected_file, 'r') as f:
            selected = json.load(f)
        
        sample_ids = list(selected.keys())
        return len(sample_ids), sample_ids[:5]  # Return count and first 5 IDs
        
    except Exception as e:
        print(f"Error reading current batch: {e}")
        return 0, []

def check_file_status(work_dir):
    """Check status of key files"""
    
    files_to_check = [
        ("data/gcp_auth_complete.txt", "GCP Authentication"),
        ("data/selected_samples.json", "Sample Selection"),
        ("data/download_complete.txt", "FASTQ Download"),
        ("data/samplesheet.csv", "Samplesheet Generation"),
        ("data/nextflow_setup_complete.txt", "Nextflow Setup"),
        ("results/master_samplesheet.csv", "Master Samplesheet"),
        ("data/processed_samples.txt", "Processed Tracking"),
        ("data/cleanup_complete.txt", "Cleanup")
    ]
    
    print("File Status:")
    for file_path, description in files_to_check:
        full_path = os.path.join(work_dir, file_path)
        if os.path.exists(full_path):
            mtime = os.path.getmtime(full_path)
            mtime_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(mtime))
            print(f"  ✓ {description}: {mtime_str}")
        else:
            print(f"  ✗ {description}: Not found")

def main():
    print('check_progress.py')
    t1 = time.time()

    # load arguments from path
    args = parse_arguments()
    
    work_dir = args.work_dir
    manifest_file = os.path.join(work_dir, "data", "manifest.json")
    processed_file = os.path.join(work_dir, "data", "processed_samples.txt")
    master_file = os.path.join(work_dir, "results", "master_samplesheet.csv")
    
    print("=" * 60)
    print("HMF RNA-seq Processing Progress Report")
    print("=" * 60)
    print(f"Working directory: {work_dir}")
    print()
    
    # Count samples
    total_samples = count_total_samples(manifest_file)
    processed_samples = count_processed_samples(processed_file)
    master_entries = count_master_samplesheet_entries(master_file)
    
    print("Sample Counts:")
    print(f"  Total samples with RNA data: {total_samples}")
    print(f"  Processed samples: {processed_samples}")
    print(f"  Master samplesheet entries: {master_entries}")
    
    if total_samples > 0:
        progress_pct = (processed_samples / total_samples) * 100
        print(f"  Progress: {progress_pct:.1f}%")
    
    remaining = max(0, total_samples - processed_samples)
    if remaining > 0:
        estimated_batches = (remaining + 19) // 20  # Round up
        print(f"  Remaining samples: {remaining}")
        print(f"  Estimated batches remaining: {estimated_batches}")
    else:
        print("  All samples processed!")
    
    print()
    
    # Current batch info
    current_count, current_samples = get_current_batch_info(work_dir)
    if current_count > 0:
        print("Current Batch:")
        print(f"  Samples in current batch: {current_count}")
        if current_samples:
            print("  Sample IDs (first 5):")
            for sample_id in current_samples:
                print(f"    - {sample_id}")
        print()
    
    # File status
    check_file_status(work_dir)
    
    print()
    print("=" * 60)
    
    print(round(time.time() - t1, 2))
    print('check_progress.py COMPLETE')

if __name__ == "__main__":
    main()
