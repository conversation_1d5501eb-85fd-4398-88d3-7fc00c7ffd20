#!/bin/bash
# Batch processing script for HMF RNA-seq data
# Runs Snakemake workflow in batches of 20 samples until all are processed

set -euo pipefail

# Configuration
WORK_DIR="/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/HMF_RNASeq_2025"
SNAKEFILE="snakemake/001-gc_rnaseq_processing.smk"
CORES=8
MAX_BATCHES=100  # Safety limit to prevent infinite loops

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}HMF RNA-seq Batch Processing Started${NC}"
echo -e "${BLUE}========================================${NC}"
echo "Working directory: $WORK_DIR"
echo "Snakefile: $SNAKEFILE"
echo "Cores: $CORES"
echo "Max batches: $MAX_BATCHES"
echo ""

# Change to working directory
cd "$WORK_DIR"

# Initialize batch counter
batch_count=0

# Create initial processed_samples.txt if it doesn't exist
if [ ! -f "data/processed_samples.txt" ]; then
    echo "Creating initial processed_samples.txt file"
    mkdir -p data
    touch data/processed_samples.txt
fi

# Main processing loop
while [ $batch_count -lt $MAX_BATCHES ]; do
    batch_count=$((batch_count + 1))
    
    echo -e "${YELLOW}========================================${NC}"
    echo -e "${YELLOW}Starting Batch $batch_count${NC}"
    echo -e "${YELLOW}========================================${NC}"
    
    # Clean up previous batch files
    echo "Cleaning up previous batch files..."
    rm -f data/selected_samples.json
    rm -f data/download_complete.txt
    rm -f data/samplesheet.csv
    rm -f data/nextflow_setup_complete.txt
    rm -f data/cleanup_complete.txt
    rm -f data/gcp_auth_complete.txt
    
    # Run Snakemake workflow for this batch
    echo "Running Snakemake workflow..."
    if snakemake -s "$SNAKEFILE" --cores $CORES --rerun-incomplete; then
        echo -e "${GREEN}Batch $batch_count completed successfully${NC}"
    else
        echo -e "${RED}ERROR: Batch $batch_count failed${NC}"
        exit 1
    fi
    
    # Check if any samples were selected for this batch
    if [ ! -f "data/selected_samples.json" ]; then
        echo -e "${YELLOW}No selected_samples.json found - no more samples to process${NC}"
        break
    fi
    
    # Check if selected_samples.json is empty or contains no samples
    if ! grep -q '"' data/selected_samples.json 2>/dev/null; then
        echo -e "${YELLOW}No samples found in selected_samples.json - all samples processed${NC}"
        break
    fi
    
    # Count samples in this batch
    sample_count=$(python3 -c "
import json
try:
    with open('data/selected_samples.json', 'r') as f:
        data = json.load(f)
    print(len(data))
except:
    print(0)
" 2>/dev/null || echo "0")
    
    if [ "$sample_count" -eq 0 ]; then
        echo -e "${YELLOW}No samples in current batch - all samples processed${NC}"
        break
    fi
    
    echo -e "${GREEN}Batch $batch_count processed $sample_count samples${NC}"
    
    # Show current progress
    total_processed=$(wc -l < data/processed_samples.txt 2>/dev/null || echo "0")
    echo -e "${BLUE}Total samples processed so far: $total_processed${NC}"
    
    # Optional: Add delay between batches
    if [ $batch_count -lt $MAX_BATCHES ]; then
        echo "Waiting 5 seconds before next batch..."
        sleep 5
    fi
    
    echo ""
done

# Final summary
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}Batch Processing Complete${NC}"
echo -e "${BLUE}========================================${NC}"

if [ $batch_count -ge $MAX_BATCHES ]; then
    echo -e "${YELLOW}WARNING: Reached maximum batch limit ($MAX_BATCHES)${NC}"
fi

# Show final statistics
total_processed=$(wc -l < data/processed_samples.txt 2>/dev/null || echo "0")
echo -e "${GREEN}Total batches processed: $batch_count${NC}"
echo -e "${GREEN}Total samples processed: $total_processed${NC}"

# Show master samplesheet location
if [ -f "results/master_samplesheet.csv" ]; then
    master_samples=$(tail -n +2 results/master_samplesheet.csv | wc -l 2>/dev/null || echo "0")
    echo -e "${GREEN}Master samplesheet: results/master_samplesheet.csv ($master_samples entries)${NC}"
fi

echo ""
echo -e "${GREEN}All processing complete!${NC}"
