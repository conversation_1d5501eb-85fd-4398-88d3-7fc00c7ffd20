# Configuration file for HMF RNA-seq processing workflow

# Working directory for all operations
work_dir: "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/HMF_RNASeq_2025"

# Google Cloud Platform settings
project_id: "reimandlab"
gcp_key_file: "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/HMF_RNASeq_2025/gcp_key.json"

# Input files
manifest_file: "manifest.json"  # Path to the HMF manifest file

# Processing parameters
num_samples: 20  # Number of samples to process

# Nextflow pipeline settings
nextflow_pipeline: "nf-core/rnaseq"
nextflow_config: "nextflow_rnaseq/nextflow.config"
reference_genome:
  fasta: "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/singh_lab_sample_processing/data/ref_data/GCF_000001405.40_GRCh38.p14_genomic.fna"
  gtf: "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/singh_lab_sample_processing/data/ref_data/GCF_000001405.40_GRCh38.p14_genomic.gtf"

# Google Cloud Storage settings
hmf_bucket: "gs://hmf-dr-141-update5/"

# Resource settings
download_threads: 1
nextflow_threads: 8

# Cleanup settings
cleanup_dry_run: false  # Set to true to see what would be deleted without actually deleting

# Strandedness for RNA-seq (auto, forward, reverse, unstranded)
strandedness: "auto"
