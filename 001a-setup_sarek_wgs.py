# Alec <PERSON>

import argparse, time, os

import pandas as pd 
import numpy as np

import glob, re

help_message = '''
Failed
'''

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Setup sarek"
    )
    parser.add_argument(
        "--fastq_dir",
        required=True,
        help=""
    )
    parser.add_argument(
        "--bin_dir",
        required=True,
        help=""
    )
    parser.add_argument(
        "--results_dir",
        required=True,
        help="results_dir"
    )

    return parser.parse_args()


# setup sarek
def setup_sarek(fastq_dir_regex, bin_dir, results_dir):
    # determine files from fastq_dir_regex
    files = pd.Series(glob.glob(fastq_dir_regex))

    # determine samples from files (excluding blood samples)
    samples = np.unique([f.split("/")[-1].split("_")[0] for f in files if "blood" not in f])

    for sample in samples:
        df = []
        
        # get patient ID from sample
        patient = sample.split("-")[0]
        
        # specific files for that sample
        sample_files = [file for file in files if re.search(f"{sample}", file)]
        
        # add blood files for the same patient
        blood_files = [file for file in files if re.search(f"{patient}-blood", file)]
        
        # combine sample and blood files
        files_oi = sample_files + blood_files

        # unique sequence runs
        uniq_runs = np.unique(pd.Series(files_oi).str.split("/").str[-1].str.split("_").str[:-1])

        # iteratively add the runs
        for run in uniq_runs:
            run = "_".join(run)
            status = '0' if 'blood' in run else '1'
            sex = sex_dict.get(patient)
            sample_name = run.split("_")[0]  # Use the full run name as sample
            lane = run.split("_")[-1] if len(run.split("_")) > 1 else "1"

            m1 = pd.Series(files_oi).str.contains(run)
            m2 = pd.Series(files_oi).str.contains('R1')
            fastq1 = np.array(files_oi)[np.logical_and(m1, m2)][0]

            m2 = pd.Series(files_oi).str.contains('R2')
            fastq2 = np.array(files_oi)[np.logical_and(m1, m2)][0]

            # add to df
            df.append([patient, sex, status, sample_name, lane, fastq1, fastq2])

        df = pd.DataFrame(df, columns = ['patient', 'sex', 'status', 'sample', 'lane', 'fastq_1', 'fastq_2'])

        # Use sample name for output files instead of patient
        outfile = os.path.join(bin_dir, f'{sample}.csv')
        df.to_csv(outfile, index=False)

        # make dir for processing
        processing_dir = os.path.join(results_dir, sample)
        if not os.path.exists(processing_dir):
            os.makedirs(processing_dir)

        # create processing file
        sarek_file = os.path.join(bin_dir, sample)
        to_write = f"""#!/bin/bash
#$ -P reimandlab
#$ -N singh_{sample}
#$ -l h_vmem=10G,h_rt=7:0:0:0
#$ -q all.q
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/sarek/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/sarek/


source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

mkdir -p {processing_dir}
cd {processing_dir}

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export NXF_OFFLINE='true'


nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/nf-core-sarek_3.4.2/3_4_2 \
--input {os.path.join(bin_dir, sample)}.csv \
--outdir {os.path.join(results_dir, sample)} \
-profile singularity -c {os.path.join(bin_dir, 'nextflow.config')} \
--tools 'freebayes,mutect2,strelka,manta,tiddit,ascat,cnvkit,controlfreec,msisensorpro,snpeff,vep,merge' \
--snpeff_cache /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/snpeff_cache/ \
--vep_cache /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/vep_cache/ \
--igenomes_base /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/igenomes/ \
-work-dir {processing_dir}/work \
-resume \
-process.maxDumpTasks 200

    """
        with open(sarek_file, "w") as outfile:
            outfile.write(to_write)


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load arguments from path
    args = parse_arguments()

    # define regex for relevant files
    file_regex = f'{args.fastq_dir}/*wgs*'

    # create files
    setup_sarek(file_regex, args.bin_dir, args.results_dir)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # sex of patients
    sex_dict = {'GBM2':'XX'}

    main()


